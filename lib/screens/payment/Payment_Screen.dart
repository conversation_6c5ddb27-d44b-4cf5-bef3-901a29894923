import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe_web/flutter_stripe_web.dart';

Future<void> pay(BuildContext context) async {
  try {
    final result = await WebStripe.instance.confirmPaymentElement(
      ConfirmPaymentElementOptions(
        confirmParams:
            ConfirmPaymentParams(return_url: "https://www.google.com"),
      ),
    );
    debugPrint(" Results ------------------- $result");
  } catch (e) {
    // Most payment methods will redirect, so we don't need to handle errors here
    // The redirect handler will take care of showing the result
    debugPrint('Payment process: ${e.toString()}');
  }
}

class PaymentScreen extends StatefulWidget {
  const PaymentScreen({super.key, this.clientSecret, this.amount, this.onPay});

  final String? clientSecret;
  final double? amount;
  final VoidCallback? onPay;

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(
        color: Colors.red,
      )),
      child: Column(
        children: [
          PaymentElement(
            autofocus: true,
            enablePostalCode: true,
            onCardChanged: (_) {},
            clientSecret: widget.clientSecret ?? '',
          ),
          ElevatedButton(onPressed: () => pay(context), child: Text('Pay'))
        ],
      ),
    );
  }
}
