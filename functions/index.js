const functions = require("firebase-functions");
const stripe = require("stripe")(functions.config().stripe.secret_key);
const cors = require("cors")({ origin: true });
const admin = require("firebase-admin");

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

exports.createPaymentIntent = functions.https.onRequest(async (req, res) => {
  // Handle CORS preflight requests
  return cors(req, res, async () => {
    try {
      // Set additional CORS headers
      res.set({
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers":
          "Content-Type, Authorization, X-Requested-With, Accept, Origin",
        "Access-Control-Max-Age": "86400", // 24 hours
        "Content-Type": "application/json",
      });

      // Handle OPTIONS preflight request
      if (req.method === "OPTIONS") {
        return res.status(200).send();
      }

      // Only allow POST requests for creating payment intents
      if (req.method !== "POST") {
        return res.status(405).json({
          error: "Method not allowed. Only POST requests are accepted.",
          success: false,
        });
      }

      // Validate request body
      const { amount, currency = "usd", email } = req.body || {};

      if (!amount || !email) {
        return res.status(400).json({
          error: "Missing required fields: amount and email are required.",
          success: false,
        });
      }
      if (typeof amount !== "number") {
        //  Parse
        amount = parseFloat(amount);
        if (amount <= 0) {
          return res.status(400).json({
            error: "Amount must be a positive number.",
            success: false,
          });
        }
      }
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({
          error: "Invalid email format.",
          success: false,
        });
      }

      // Find or create customer
      let customer;
      try {
        const customers = await stripe.customers.list({ email, limit: 1 });
        customer = customers.data[0]
          ? customers.data[0]
          : await stripe.customers.create({ email });
      } catch (stripeError) {
        console.error("Error handling customer:", stripeError);
        return res.status(500).json({
          error: "Failed to create or retrieve customer.",
          success: false,
        });
      }

      // Create ephemeral key
      let ephemeralKey;
      try {
        ephemeralKey = await stripe.ephemeralKeys.create(
          { customer: customer.id },
          { apiVersion: "2020-08-27" }
        );
      } catch (stripeError) {
        console.error("Error creating ephemeral key:", stripeError);
        return res.status(500).json({
          error: "Failed to create ephemeral key.",
          success: false,
        });
      }

      // Create payment intent
      let paymentIntent;
      try {
        paymentIntent = await stripe.paymentIntents.create({
          amount: Math.round(amount), // Ensure amount is an integer
          currency,
          customer: customer.id,
          automatic_payment_methods: {
            enabled: true,
          },
        });
      } catch (stripeError) {
        console.error("Error creating payment intent:", stripeError);
        return res.status(500).json({
          error: "Failed to create payment intent.",
          success: false,
        });
      }

      // Return successful response
      return res.status(200).json({
        paymentIntent: paymentIntent.client_secret,
        ephemeralKey: ephemeralKey.secret,
        customer: customer.id,
        customerEmail: customer.email,
        success: true,
      });
    } catch (error) {
      console.error("Unexpected error creating payment intent:", error);
      return res.status(500).json({
        error: "An unexpected error occurred. Please try again.",
        success: false,
      });
    }
  });
});

// Handle payment completion and update wallet
exports.handlePaymentCompletion = functions.https.onRequest(
  async (req, res) => {
    return cors(req, res, async () => {
      try {
        // Set CORS headers
        res.set({
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
          "Access-Control-Allow-Headers":
            "Content-Type, Authorization, X-Requested-With, Accept, Origin",
          "Access-Control-Max-Age": "86400",
          "Content-Type": "application/json",
        });

        if (req.method === "OPTIONS") {
          return res.status(200).send();
        }

        if (req.method !== "POST") {
          return res.status(405).json({
            error: "Method not allowed. Only POST requests are accepted.",
            success: false,
          });
        }

        const { paymentIntentId, userId } = req.body || {};

        if (!paymentIntentId || !userId) {
          return res.status(400).json({
            error:
              "Missing required fields: paymentIntentId and userId are required.",
            success: false,
          });
        }

        // Retrieve payment intent from Stripe
        const paymentIntent = await stripe.paymentIntents.retrieve(
          paymentIntentId
        );

        if (paymentIntent.status !== "succeeded") {
          return res.status(400).json({
            error: `Payment not completed. Status: ${paymentIntent.status}`,
            success: false,
          });
        }

        // Calculate amount in dollars (Stripe uses cents)
        const amount = paymentIntent.amount / 100;

        // Get Firestore references
        const db = admin.firestore();
        const walletRef = db.collection("wallets").doc(userId);
        const transactionsRef = walletRef.collection("transactions");

        // Check if this payment has already been processed
        const existingTransaction = await transactionsRef
          .where("externalTransactionId", "==", paymentIntentId)
          .limit(1)
          .get();

        if (!existingTransaction.empty) {
          return res.status(200).json({
            message: "Payment already processed",
            success: true,
            alreadyProcessed: true,
          });
        }

        // Use a transaction to ensure atomicity
        await db.runTransaction(async (transaction) => {
          // Get current wallet data
          const walletDoc = await transaction.get(walletRef);
          const currentBalance = walletDoc.exists
            ? walletDoc.data().balance || 0
            : 0;
          const newBalance = currentBalance + amount;

          // Create transaction record matching the app's schema (except timestamp)
          const transactionData = {
            type: "credit",
            amount: amount,
            description: `Wallet funding via Stripe - $${amount.toFixed(2)}`,
            timestamp: Date.now(), // Use milliseconds since epoch to match app schema
            status: "completed",
            postId: null,
            paymentMethodId: paymentIntent.payment_method,
            externalTransactionId: paymentIntentId,
            metadata: {
              paymentIntentId: paymentIntentId,
              stripePaymentMethod: paymentIntent.payment_method,
              processedBy: "firebase-function",
            },
          };

          // Add transaction
          const newTransactionRef = transactionsRef.doc();
          transaction.set(newTransactionRef, transactionData);

          // Update wallet balance
          transaction.set(
            walletRef,
            {
              balance: newBalance,
              lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
            },
            { merge: true }
          );
        });

        return res.status(200).json({
          message: "Payment processed successfully",
          success: true,
          amount: amount,
          newBalance: null, // We don't return the balance for security
        });
      } catch (error) {
        console.error("Error processing payment completion:", error);
        return res.status(500).json({
          error: "Failed to process payment completion",
          success: false,
        });
      }
    });
  }
);

/**
 * Cloud Function to automatically delete posts older than 48 hours
 * Runs every hour using Cloud Scheduler
 */
exports.cleanupOldPosts = functions.pubsub
  .schedule("0 * * * *") // Run every hour at minute 0
  .timeZone("UTC")
  .onRun(async (context) => {
    console.log("Starting cleanup of old posts...");

    try {
      const db = admin.firestore();
      const now = admin.firestore.Timestamp.now();

      // Calculate 48 hours ago
      const fortyEightHoursAgo = new Date(
        now.toDate().getTime() - 48 * 60 * 60 * 1000
      );
      const cutoffTimestamp =
        admin.firestore.Timestamp.fromDate(fortyEightHoursAgo);

      console.log(
        `Deleting posts older than: ${fortyEightHoursAgo.toISOString()}`
      );

      // Query posts older than 48 hours
      const oldPostsQuery = db
        .collection("posts")
        .where("createdAt", "<", cutoffTimestamp)
        .limit(500); // Process in batches to avoid timeout

      const snapshot = await oldPostsQuery.get();

      if (snapshot.empty) {
        console.log("No old posts found to delete");
        return null;
      }

      console.log(`Found ${snapshot.size} posts to delete`);

      // Create batch for efficient deletion
      const batch = db.batch();
      let deleteCount = 0;

      snapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
        deleteCount++;

        const postData = doc.data();
        console.log(
          `Queued for deletion: Post ${doc.id} from ${postData.createdAt
            ?.toDate()
            ?.toISOString()}`
        );
      });

      // Execute batch deletion
      await batch.commit();

      console.log(`Successfully deleted ${deleteCount} old posts`);

      // Log statistics
      const remainingPostsSnapshot = await db.collection("posts").get();
      console.log(
        `Remaining posts in database: ${remainingPostsSnapshot.size}`
      );

      return {
        success: true,
        deletedCount: deleteCount,
        remainingCount: remainingPostsSnapshot.size,
        cutoffTime: fortyEightHoursAgo.toISOString(),
      };
    } catch (error) {
      console.error("Error during post cleanup:", error);

      // Don't throw error to prevent function retries
      // Just log and return error status
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  });

/**
 * Manual trigger function for testing cleanup
 * Can be called via HTTP request for testing purposes
 */
exports.manualCleanupOldPosts = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    console.log("Manual cleanup triggered");

    // Set CORS headers
    res.set({
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers":
        "Content-Type, Authorization, X-Requested-With, Accept, Origin",
      "Access-Control-Max-Age": "86400",
      "Content-Type": "application/json",
    });

    if (req.method === "OPTIONS") {
      return res.status(200).send();
    }

    try {
      const db = admin.firestore();
      const now = admin.firestore.Timestamp.now();

      // Calculate 48 hours ago
      const fortyEightHoursAgo = new Date(
        now.toDate().getTime() - 48 * 60 * 60 * 1000
      );
      const cutoffTimestamp =
        admin.firestore.Timestamp.fromDate(fortyEightHoursAgo);

      console.log(
        `Manual cleanup: Deleting posts older than: ${fortyEightHoursAgo.toISOString()}`
      );

      // Query posts older than 48 hours
      const oldPostsQuery = db
        .collection("posts")
        .where("createdAt", "<", cutoffTimestamp)
        .limit(500);

      const snapshot = await oldPostsQuery.get();

      if (snapshot.empty) {
        console.log("Manual cleanup: No old posts found to delete");
        return res.status(200).json({
          message: "No old posts found to delete",
          deletedCount: 0,
          success: true,
        });
      }

      // Create batch for efficient deletion
      const batch = db.batch();
      let deleteCount = 0;

      snapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
        deleteCount++;
      });

      // Execute batch deletion
      await batch.commit();

      console.log(
        `Manual cleanup: Successfully deleted ${deleteCount} old posts`
      );

      res.status(200).json({
        message: "Manual cleanup completed successfully",
        deletedCount: deleteCount,
        cutoffTime: fortyEightHoursAgo.toISOString(),
        success: true,
      });
    } catch (error) {
      console.error("Manual cleanup failed:", error);

      res.status(500).json({
        message: "Manual cleanup failed",
        error: error.message,
        success: false,
      });
    }
  });
});

/**
 * Function to get cleanup statistics
 * Returns information about post counts and cleanup history
 */
exports.getCleanupStats = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    // Set CORS headers
    res.set({
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers":
        "Content-Type, Authorization, X-Requested-With, Accept, Origin",
      "Access-Control-Max-Age": "86400",
      "Content-Type": "application/json",
    });

    if (req.method === "OPTIONS") {
      return res.status(200).send();
    }

    try {
      const db = admin.firestore();
      const now = admin.firestore.Timestamp.now();

      // Get total post count
      const totalPostsSnapshot = await db.collection("posts").get();
      const totalPosts = totalPostsSnapshot.size;

      // Get posts from last 24 hours
      const twentyFourHoursAgo = new Date(
        now.toDate().getTime() - 24 * 60 * 60 * 1000
      );
      const last24HoursSnapshot = await db
        .collection("posts")
        .where(
          "createdAt",
          ">=",
          admin.firestore.Timestamp.fromDate(twentyFourHoursAgo)
        )
        .get();

      // Get posts from last 48 hours
      const fortyEightHoursAgo = new Date(
        now.toDate().getTime() - 48 * 60 * 60 * 1000
      );
      const last48HoursSnapshot = await db
        .collection("posts")
        .where(
          "createdAt",
          ">=",
          admin.firestore.Timestamp.fromDate(fortyEightHoursAgo)
        )
        .get();

      // Get posts older than 48 hours (candidates for deletion)
      const oldPostsSnapshot = await db
        .collection("posts")
        .where(
          "createdAt",
          "<",
          admin.firestore.Timestamp.fromDate(fortyEightHoursAgo)
        )
        .get();

      const stats = {
        totalPosts: totalPosts,
        postsLast24Hours: last24HoursSnapshot.size,
        postsLast48Hours: last48HoursSnapshot.size,
        postsOlderThan48Hours: oldPostsSnapshot.size,
        nextCleanupWillDelete: oldPostsSnapshot.size,
        cutoffTime: fortyEightHoursAgo.toISOString(),
        timestamp: new Date().toISOString(),
        success: true,
      };

      console.log("Cleanup stats:", stats);

      res.status(200).json(stats);
    } catch (error) {
      console.error("Error getting cleanup stats:", error);

      res.status(500).json({
        error: error.message,
        timestamp: new Date().toISOString(),
        success: false,
      });
    }
  });
});
